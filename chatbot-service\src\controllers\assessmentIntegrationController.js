const { Conversation, Message } = require('../models');
const ContextService = require('../services/contextService');
const AssessmentEventHandler = require('../services/assessmentEventHandler');
const logger = require('../utils/logger');

/**
 * AssessmentIntegrationController for handling assessment-specific API endpoints
 * Manages assessment-to-chatbot integration and personalized features
 */
class AssessmentIntegrationController {
  constructor() {
    this.contextService = new ContextService();
    this.assessmentEventHandler = new AssessmentEventHandler();
  }

  /**
   * Create conversation from assessment
   * POST /api/chatbot/conversations/from-assessment
   */
  async createFromAssessment(req, res) {
    try {
      const { assessment_id, title, auto_start_message = true } = req.body;
      const userId = req.user.id;

      logger.info('Creating conversation from assessment', {
        userId,
        assessmentId: assessment_id,
        autoStartMessage: auto_start_message
      });

      // Validate required fields
      if (!assessment_id) {
        return res.status(400).json({ 
          error: 'Assessment ID is required',
          code: 'MISSING_ASSESSMENT_ID'
        });
      }

      // Check if assessment conversation already exists
      const existingConversation = await this.findAssessmentConversation(userId, assessment_id);
      
      if (existingConversation) {
        logger.info('Assessment conversation already exists', {
          conversationId: existingConversation.id,
          userId,
          assessmentId: assessment_id
        });

        // Get suggestions for existing conversation
        const suggestions = await this.generateSuggestionsForConversation(existingConversation);

        return res.json({
          conversation: existingConversation,
          initial_message: null,
          suggestions,
          message: 'Assessment conversation already exists'
        });
      }

      // Validate assessment exists and belongs to user
      const assessmentData = await this.validateAssessmentAccess(userId, assessment_id);
      
      if (!assessmentData) {
        return res.status(404).json({ 
          error: 'Assessment not found or access denied',
          code: 'ASSESSMENT_NOT_FOUND'
        });
      }

      // Create conversation with assessment context
      const result = await this.assessmentEventHandler.createAssessmentConversation(
        userId, 
        assessment_id, 
        assessmentData
      );

      logger.info('Assessment conversation created successfully', {
        conversationId: result.conversation.id,
        userId,
        assessmentId: assessment_id,
        hasWelcomeMessage: !!result.welcomeMessage
      });

      res.status(201).json({
        conversation: result.conversation,
        initial_message: auto_start_message ? result.welcomeMessage : null,
        suggestions: result.suggestions || []
      });

    } catch (error) {
      logger.error('Error creating conversation from assessment', {
        userId: req.user?.id,
        assessmentId: req.body?.assessment_id,
        error: error.message,
        stack: error.stack
      });

      res.status(500).json({ 
        error: 'Failed to create assessment conversation',
        code: 'CONVERSATION_CREATION_ERROR'
      });
    }
  }

  /**
   * Check if user has assessment ready for chatbot
   * GET /api/chatbot/assessment-ready/:userId
   */
  async checkAssessmentReady(req, res) {
    try {
      const { userId } = req.params;
      
      // Verify user can access this endpoint (same user or admin)
      if (req.user.id !== userId && req.user.user_type !== 'admin') {
        return res.status(403).json({
          error: 'Access denied',
          code: 'ACCESS_DENIED'
        });
      }

      logger.info('Checking assessment readiness', { userId });

      // Check if user has completed assessment
      const assessmentStatus = await this.getLatestAssessmentStatus(userId);
      
      if (!assessmentStatus.has_assessment) {
        return res.json({
          has_assessment: false,
          ready_for_chatbot: false,
          message: 'No assessment found. Please complete your career assessment first.'
        });
      }

      // Check if conversation already exists
      const existingConversation = await this.findAssessmentConversation(
        userId, 
        assessmentStatus.assessment_id
      );

      res.json({
        has_assessment: true,
        assessment_date: assessmentStatus.assessment_date,
        assessment_id: assessmentStatus.assessment_id,
        conversation_exists: !!existingConversation,
        conversation_id: existingConversation?.id || null,
        ready_for_chatbot: true
      });

    } catch (error) {
      logger.error('Error checking assessment readiness', {
        userId: req.params.userId,
        error: error.message
      });

      res.status(500).json({ 
        error: 'Failed to check assessment status',
        code: 'ASSESSMENT_CHECK_ERROR'
      });
    }
  }

  /**
   * Generate suggestions for conversation
   * GET /api/chatbot/conversations/:conversationId/suggestions
   */
  async generateSuggestions(req, res) {
    try {
      const { conversationId } = req.params;
      const userId = req.user.id;

      logger.info('Generating suggestions for conversation', {
        conversationId,
        userId
      });

      const conversation = await Conversation.findOne({
        where: { id: conversationId, user_id: userId }
      });

      if (!conversation) {
        return res.status(404).json({ 
          error: 'Conversation not found',
          code: 'CONVERSATION_NOT_FOUND'
        });
      }

      if (conversation.context_type !== 'assessment') {
        return res.status(400).json({ 
          error: 'Suggestions only available for assessment conversations',
          code: 'INVALID_CONVERSATION_TYPE'
        });
      }

      const suggestions = await this.generateSuggestionsForConversation(conversation);

      res.json({ suggestions });

    } catch (error) {
      logger.error('Error generating suggestions', {
        conversationId: req.params.conversationId,
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({ 
        error: 'Failed to generate suggestions',
        code: 'SUGGESTION_GENERATION_ERROR'
      });
    }
  }

  /**
   * Auto-initialize assessment conversation
   * POST /api/chatbot/conversations/auto-initialize
   */
  async autoInitialize(req, res) {
    try {
      const userId = req.user.id;

      logger.info('Auto-initializing assessment conversation', { userId });

      // Get latest assessment for user
      const assessmentStatus = await this.getLatestAssessmentStatus(userId);
      
      if (!assessmentStatus.has_assessment) {
        return res.status(404).json({
          error: 'No assessment found for user',
          code: 'NO_ASSESSMENT_FOUND'
        });
      }

      // Check if conversation already exists
      const existingConversation = await this.findAssessmentConversation(
        userId, 
        assessmentStatus.assessment_id
      );

      if (existingConversation) {
        const suggestions = await this.generateSuggestionsForConversation(existingConversation);
        
        return res.json({
          conversation: existingConversation,
          suggestions,
          message: 'Assessment conversation already exists'
        });
      }

      // Create new conversation
      const assessmentData = await this.validateAssessmentAccess(userId, assessmentStatus.assessment_id);
      
      if (!assessmentData) {
        return res.status(404).json({
          error: 'Assessment data not accessible',
          code: 'ASSESSMENT_DATA_ERROR'
        });
      }

      const result = await this.assessmentEventHandler.createAssessmentConversation(
        userId, 
        assessmentStatus.assessment_id, 
        assessmentData
      );

      res.status(201).json({
        conversation: result.conversation,
        initial_message: result.welcomeMessage,
        suggestions: result.suggestions || []
      });

    } catch (error) {
      logger.error('Error auto-initializing conversation', {
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({ 
        error: 'Failed to auto-initialize conversation',
        code: 'AUTO_INIT_ERROR'
      });
    }
  }

  /**
   * Find assessment conversation for user
   * @param {string} userId - User ID
   * @param {string} assessmentId - Assessment ID
   * @returns {Object|null} Conversation or null
   */
  async findAssessmentConversation(userId, assessmentId) {
    try {
      const conversation = await Conversation.findOne({
        where: {
          user_id: userId,
          context_type: 'assessment',
          status: 'active'
        },
        order: [['created_at', 'DESC']]
      });

      if (conversation && conversation.context_data?.assessment_id === assessmentId) {
        return conversation;
      }

      return null;
    } catch (error) {
      logger.error('Error finding assessment conversation', {
        userId,
        assessmentId,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Validate assessment access for user
   * @param {string} userId - User ID
   * @param {string} assessmentId - Assessment ID
   * @returns {Object|null} Assessment data or null
   */
  async validateAssessmentAccess(userId, assessmentId) {
    try {
      // This would typically call the archive service to validate access
      // For now, we'll use the context service to fetch assessment data
      const assessmentData = await this.contextService.fetchAssessmentData(assessmentId);
      
      // Additional validation could be added here to ensure user owns the assessment
      return assessmentData;
    } catch (error) {
      logger.error('Error validating assessment access', {
        userId,
        assessmentId,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Get latest assessment status for user
   * @param {string} userId - User ID
   * @returns {Object} Assessment status
   */
  async getLatestAssessmentStatus(userId) {
    try {
      // Call archive service to get user's latest assessment results
      const axios = require('axios');
      const archiveServiceUrl = process.env.ARCHIVE_SERVICE_URL || 'http://archive-service:3004';
      const internalServiceKey = process.env.INTERNAL_SERVICE_KEY || 'default-internal-key';

      logger.info('Fetching assessment status from archive service', { userId });

      const response = await axios.get(
        `${archiveServiceUrl}/api/archive/results?limit=1&sort=created_at&order=DESC`,
        {
          headers: {
            'x-internal-service': 'true',
            'x-service-key': internalServiceKey,
            'Content-Type': 'application/json',
            'x-user-id': userId // Pass user ID for internal service access
          },
          timeout: 10000
        }
      );

      if (response.data.success && response.data.data.results && response.data.data.results.length > 0) {
        const latestResult = response.data.data.results[0]; // Results are ordered by created_at DESC

        logger.info('Assessment found for user', {
          userId,
          resultId: latestResult.id,
          status: latestResult.status,
          createdAt: latestResult.created_at
        });

        return {
          has_assessment: true,
          assessment_id: latestResult.id,
          assessment_date: latestResult.created_at,
          status: latestResult.status
        };
      }

      logger.info('No assessment found for user', { userId });
      return {
        has_assessment: false,
        assessment_id: null,
        assessment_date: null
      };
    } catch (error) {
      logger.error('Error getting assessment status from archive service', {
        userId,
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      });

      return {
        has_assessment: false,
        assessment_id: null,
        assessment_date: null
      };
    }
  }

  /**
   * Generate suggestions for conversation
   * @param {Object} conversation - Conversation object
   * @returns {Array} Array of suggestions
   */
  async generateSuggestionsForConversation(conversation) {
    try {
      if (!conversation.context_data?.analysis_results) {
        return [
          "What career paths would be best for my personality?",
          "How can I use my strengths in my career?",
          "What skills should I focus on developing?",
          "What work environments suit me best?"
        ];
      }

      return await this.assessmentEventHandler.generateAssessmentSuggestions(
        conversation.context_data.analysis_results
      );
    } catch (error) {
      logger.error('Error generating suggestions for conversation', {
        conversationId: conversation.id,
        error: error.message
      });
      
      return [
        "Tell me about my career assessment results",
        "What are my strongest personality traits?",
        "What career options match my profile?",
        "How can I develop my skills further?"
      ];
    }
  }
}

module.exports = AssessmentIntegrationController;
